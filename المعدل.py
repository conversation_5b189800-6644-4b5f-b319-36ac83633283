import sqlite3
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, font
from PIL import Image, ImageTk
import random
import string
import json
import os
import logging
import hashlib
import base64
import re
import ipaddress
import shutil
import threading
from datetime import datetime, timedelta
from pathlib import Path
try:
    import routeros_api
    ROUTEROS_AVAILABLE = True
except ImportError:
    ROUTEROS_AVAILABLE = False
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import mm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import qrcode
import io
from reportlab.lib.utils import ImageReader
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

class MikroTikCardGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("🚀 مولد كروت وسكريبتات MikroTik - الإصدار المعدل")
        self.root.geometry("1400x900")  # حجم نافذة محسن
        self.root.resizable(True, True)

        # تحسين الحد الأدنى لحجم النافذة
        self.root.minsize(1000, 700)

        # تحسين أيقونة النافذة (إذا كانت متوفرة)
        try:
            # يمكن إضافة أيقونة مخصصة هنا
            pass
        except:
            pass

        # إعداد نظام السجلات
        self.setup_logging()
        self.logger.info("تم بدء تشغيل التطبيق المعدل")

        # إعداد المجلدات الأساسية
        self.setup_directories()

        # إعداد التشفير
        self.encryption_key = self.get_or_create_encryption_key()

        self.system_type = None
        self.generated_credentials = []
        self.background_image = None
        self.background_image_path = None
        self.settings_file = "config/mikrotik_settings.json"
        self.user_manager_templates_file = "config/mikrotik_user_manager_templates.json"
        self.hotspot_templates_file = "config/mikrotik_hotspot_templates.json"
        self.profiles = []
        self.last_serial = 0
        self.dragging_element = None
        self.element_positions = {}
        self.canvas_elements = {}
        self.progress_var = tk.DoubleVar()
        self.connection_status_var = tk.StringVar(value="غير متصل")
        self.user_email = tk.StringVar()
        self.caller_id_bind_var = tk.BooleanVar()
        self.run_script_before_cleanup_var = tk.BooleanVar()
        self.script_to_run_entry = ttk.Entry(self.root)  # حقل إدخال اسم السكربت
        
        # إضافة متغير لفتح PDF تلقائياً (التعديل الجديد)
        self.auto_open_pdf_var = tk.BooleanVar(value=True)

        self.api_connection = None
        self.api = None

        # إعداد الواجهة
        self.setup_styles()

        try:
            self.initialize_database()
            self.setup_selection_gui()
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.logger.info("تم إعداد التطبيق المعدل بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في إعداد التطبيق: {str(e)}")
            messagebox.showerror("خطأ", f"فشل في إعداد التطبيق: {str(e)}")

    def setup_logging(self):
        """إعداد نظام السجلات"""
        try:
            # إنشاء مجلد السجلات إذا لم يكن موجوداً
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)

            # إعداد تنسيق السجل
            log_format = '%(asctime)s - %(levelname)s - %(message)s'

            # إعداد ملف السجل
            log_file = log_dir / f"mikrotik_generator_{datetime.now().strftime('%Y%m%d')}.log"

            # إعداد المسجل
            logging.basicConfig(
                level=logging.INFO,
                format=log_format,
                handlers=[
                    logging.FileHandler(log_file, encoding='utf-8'),
                    logging.StreamHandler()
                ]
            )

            self.logger = logging.getLogger(__name__)

        except Exception as e:
            # في حالة فشل إعداد السجلات، استخدم مسجل افتراضي
            self.logger = logging.getLogger(__name__)
            self.logger.setLevel(logging.INFO)

    def setup_directories(self):
        """إنشاء المجلدات الأساسية"""
        try:
            directories = ["config", "backups", "exports", "logs", "templates"]
            for directory in directories:
                Path(directory).mkdir(exist_ok=True)
            self.logger.info("تم إنشاء المجلدات الأساسية")
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء المجلدات: {str(e)}")

    def get_or_create_encryption_key(self):
        """الحصول على مفتاح التشفير أو إنشاؤه"""
        try:
            key_file = Path("config/encryption.key")
            if key_file.exists():
                with open(key_file, 'rb') as f:
                    return f.read()
            else:
                # إنشاء مفتاح جديد
                key = os.urandom(32)  # 256-bit key
                with open(key_file, 'wb') as f:
                    f.write(key)
                self.logger.info("تم إنشاء مفتاح تشفير جديد")
                return key
        except Exception as e:
            self.logger.error(f"خطأ في إعداد التشفير: {str(e)}")
            return os.urandom(32)  # مفتاح مؤقت
